variable "do_token" {
  type = string
  default = ""
}

variable "operation_endpoint" {
  type = string
  default = ""
}

variable "access_token" {
  type = string
  default = ""
}

data "http" "deployments" {
  url = var.operation_endpoint
  method = "GET"
  request_headers = {
    Accept = "application/json"
    Authorization = var.access_token
  }
}

variable "cloudflare_api_token" {
  type = string
  default = ""
}

variable "cloudflare_zone_id" {
  type = string
  default = ""
}

variable "dockerhub_username" {
  description = "DockerHub username"
  type        = string
  sensitive   = true
}

variable "dockerhub_password" {
  description = "DockerHub password"
  type        = string
  sensitive   = true
}

variable "dockerhub_email" {
  description = "DockerHub email"
  type        = string
}

variable "aws_access_key_id" {
  description = "AWS Access Key ID"
  type        = string
  sensitive   = true
}

variable "aws_secret_access_key" {
  description = "AWS Secret Access Key"
  type        = string
  sensitive   = true
}
