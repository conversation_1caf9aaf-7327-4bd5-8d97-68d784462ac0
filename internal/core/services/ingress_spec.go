package services

import (
	"errors"
	"fmt"
	"ops-api/internal/core/domain"
	"ops-api/internal/core/ports"
)

type IngressSpecService struct {
	ingressSpecRepo ports.IngressSpecRepository
	serviceRepo     ports.ServiceRepository
	ingressRepo     ports.IngressRepository
	namespaceRepo   ports.NamespaceRepository
}

func NewIngressSpecService(ingressSpecRepo ports.IngressSpecRepository, serviceRepo ports.ServiceRepository, ingressRepo ports.IngressRepository, namespaceRepo ports.NamespaceRepository) ports.IngressSpecService {
	return &IngressSpecService{
		ingressSpecRepo: ingressSpecRepo,
		serviceRepo:     serviceRepo,
		ingressRepo:     ingressRepo,
		namespaceRepo:   namespaceRepo,
	}
}

func (s *IngressSpecService) Create(host, path string, port, serviceID, ingressID uint64) (*domain.IngressSpec, error) {
	if host == "" {
		return nil, errors.New("host is required")
	}
	if path == "" {
		return nil, errors.New("path is required")
	}
	if port == 0 || port > 65535 {
		return nil, errors.New("port must be between 1 and 65535")
	}
	if serviceID == 0 {
		return nil, errors.New("service ID is required")
	}
	if ingressID == 0 {
		return nil, errors.New("ingress ID is required")
	}

	// Validate that ingress exists
	_, err := s.ingressRepo.FindByID(ingressID)
	if err != nil {
		return nil, errors.New("ingress not found")
	}

	ingressSpec := &domain.IngressSpec{
		Host:      host,
		Path:      path,
		Port:      port,
		ServiceID: serviceID,
		IngressID: ingressID,
	}

	if err := s.ingressSpecRepo.Insert(ingressSpec); err != nil {
		return nil, err
	}

	return s.ingressSpecRepo.FindByID(ingressSpec.ID)
}

func (s *IngressSpecService) GetAll(filter *ports.IngressSpecFilter) ([]*domain.IngressSpec, error) {
	return s.ingressSpecRepo.FindAll(filter)
}

func (s *IngressSpecService) GetByID(id uint64) (*domain.IngressSpec, error) {
	if id == 0 {
		return nil, errors.New("id is required")
	}

	ingressSpec, err := s.ingressSpecRepo.FindByID(id)
	if err != nil {
		return nil, errors.New("ingress spec not found")
	}

	return ingressSpec, nil
}

func (s *IngressSpecService) Update(id uint64, host, path string, port, serviceID uint64) (*domain.IngressSpec, error) {
	if id == 0 {
		return nil, errors.New("id is required")
	}
	if host == "" {
		return nil, errors.New("host is required")
	}
	if path == "" {
		return nil, errors.New("path is required")
	}
	if port == 0 || port > 65535 {
		return nil, errors.New("port must be between 1 and 65535")
	}
	if serviceID == 0 {
		return nil, errors.New("service ID is required")
	}

	ingressSpec, err := s.ingressSpecRepo.FindByID(id)
	if err != nil {
		return nil, errors.New("ingress spec not found")
	}

	ingressSpec.Host = host
	ingressSpec.Path = path
	ingressSpec.Port = port
	ingressSpec.ServiceID = serviceID

	if err := s.ingressSpecRepo.Update(ingressSpec); err != nil {
		return nil, err
	}

	return s.ingressSpecRepo.FindByID(id)
}

func (s *IngressSpecService) Delete(id uint64) error {
	if id == 0 {
		return errors.New("id is required")
	}

	_, err := s.ingressSpecRepo.FindByID(id)
	if err != nil {
		return errors.New("ingress spec not found")
	}

	return s.ingressSpecRepo.Delete(id)
}

// UpdateHostsByNamespace updates the host field of all ingress-specs within a specific namespace
// by using the service name as the subdomain and combining it with the new dns
func (s *IngressSpecService) UpdateHostsByNamespace(namespaceID uint64, newDomain string) (int, error) {
	if namespaceID == 0 {
		return 0, errors.New("namespace ID is required")
	}
	if newDomain == "" {
		return 0, errors.New("new dns is required")
	}

	// Validate that namespace exists
	_, err := s.namespaceRepo.FindByID(namespaceID)
	if err != nil {
		return 0, errors.New("namespace not found")
	}

	// Find all ingress specs for the namespace
	ingressSpecs, err := s.ingressSpecRepo.FindByNamespaceID(namespaceID)
	if err != nil {
		return 0, fmt.Errorf("failed to find ingress specs for namespace: %w", err)
	}

	if len(ingressSpecs) == 0 {
		return 0, nil // No ingress specs found, but not an error
	}

	updatedCount := 0
	for _, ingressSpec := range ingressSpecs {
		// Skip if service is not loaded or service name is empty
		if ingressSpec.Service == nil || ingressSpec.Service.Name == "" {
			continue
		}

		// Use service name as the subdomain portion
		serviceName := ingressSpec.Service.Name
		ingressSpec.Host = serviceName + "." + newDomain

		// Update the ingress spec
		if err := s.ingressSpecRepo.Update(ingressSpec); err != nil {
			return updatedCount, fmt.Errorf("failed to update ingress spec ID %d: %w", ingressSpec.ID, err)
		}
		updatedCount++
	}

	return updatedCount, nil
}
