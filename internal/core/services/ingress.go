package services

import (
	"errors"
	"ops-api/internal/core/domain"
	"ops-api/internal/core/ports"
)

type IngressService struct {
	ingressRepo   ports.IngressRepository
	namespaceRepo ports.NamespaceRepository
}

func NewIngressService(ingressRepo ports.IngressRepository, namespaceRepo ports.NamespaceRepository) ports.IngressService {
	return &IngressService{
		ingressRepo:   ingressRepo,
		namespaceRepo: namespaceRepo,
	}
}

func (s *IngressService) Create(name, class string, namespaceID uint64) (*domain.Ingress, error) {
	if name == "" {
		return nil, errors.New("name is required")
	}
	if namespaceID == 0 {
		return nil, errors.New("namespace ID is required")
	}

	_, err := s.namespaceRepo.FindByID(namespaceID)
	if err != nil {
		return nil, errors.New("namespace not found")
	}

	ingress := &domain.Ingress{
		Name:        name,
		Class:       class,
		NamespaceID: namespaceID,
		StatusID:    1,
	}

	if err := s.ingressRepo.Insert(ingress); err != nil {
		return nil, err
	}

	return s.ingressRepo.FindByID(ingress.ID)
}

func (s *IngressService) GetAll(filter *ports.IngressFilter) ([]*domain.Ingress, error) {
	return s.ingressRepo.FindAll(filter)
}

func (s *IngressService) GetByID(id uint64) (*domain.Ingress, error) {
	if id == 0 {
		return nil, errors.New("id is required")
	}

	ingress, err := s.ingressRepo.FindByID(id)
	if err != nil {
		return nil, errors.New("ingress not found")
	}

	return ingress, nil
}

func (s *IngressService) Update(id uint64, name, class string, statusID uint64) (*domain.Ingress, error) {
	if id == 0 {
		return nil, errors.New("id is required")
	}
	if name == "" {
		return nil, errors.New("name is required")
	}

	if statusID == 0 {
		statusID = 1
	}

	ingress, err := s.ingressRepo.FindByID(id)
	if err != nil {
		return nil, errors.New("ingress not found")
	}

	ingress.Name = name
	ingress.Class = class
	ingress.StatusID = statusID

	if err := s.ingressRepo.Update(ingress); err != nil {
		return nil, err
	}

	return s.ingressRepo.FindByID(id)
}

func (s *IngressService) Delete(id uint64) error {
	if id == 0 {
		return errors.New("id is required")
	}

	_, err := s.ingressRepo.FindByID(id)
	if err != nil {
		return errors.New("ingress not found")
	}

	return s.ingressRepo.Delete(id)
}
